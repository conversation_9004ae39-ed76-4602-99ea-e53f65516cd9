package com.nercar.contract.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.nercar.contract.Filter.RequestContextHolder;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.PageDataResult;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.domain.ContractInfoDomain;
import com.nercar.contract.domain.PageDataInfo;
import com.nercar.contract.dto.*;
import com.nercar.contract.entity.*;
import com.nercar.contract.mapper.StatusBaseMapper;
import com.nercar.contract.service.*;
import com.nercar.contract.utils.WordTableTextReplacer;
import com.nercar.contract.validator.ValidationResult;
import com.nercar.contract.validator.ValidatorImpl;
import com.nercar.contract.vo.*;
import com.nercar.contract.xiaominge.utils.wordUtils.WordToPdfUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ContentDisposition;
import java.io.ByteArrayOutputStream;
import java.util.zip.ZipOutputStream;
import java.util.zip.ZipEntry;


import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.ArrayList;

/**
 * @description: 销售公司-首页
 * @author: zmc
 * @date: 2024/9/20
 */
@Api(tags = "销售公司")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sale")
public class SaleController {
    private final ISpecificationInfoService specificationInfoService;
    private final IFinalOpinionService finalOpinionService;
    private final IContractInfoService contractInfoService;
    private final ISpecificationBaseService specificationBaseService;
    private final IReviewInfoService reviewInfoService;
    private final IOverallOpinionService overallOpinionService;
    private final IReviewCommentService reviewCommentService;

    private final ICustomerInfoService customerInfoService;

    private final ISteelGradeBaseService steelGradeBaseService;
    private final ISteelTypeBaseService steelTypeBaseService;
    private final IItemBaseService itemBaseService;
    private final IReviewTypeService reviewTypeService;

    private final ITechnicalStandardBaseService technicalStandardBaseService;

    private final IOutsourcingService outsourcingService;

    private final IStandardBaseService standardBaseService;

    private final IDeliveryStatusBaseService deliveryStatusBaseService;

    private final IProcessingPurposeBaseService processingPurposeBaseService;

    private final ValidatorImpl validator;
    private final UserService userService;

    private final StatusBaseMapper statusBaseMapper;

    // 合同模板配置
    @Value("${contract.template.base-path}")
    private String templateBasePath;

    @Value("${contract.template.review-template}")
    private String reviewTemplateName;

    @ApiOperation("待处理评审-核定外委业务-提交")
    @PostMapping("/submitOutsourcing")
    public CommonResult<Boolean> submitOutsourcing(@RequestBody OutsourcingDTO outsourcingDTO) throws BusinessException {
        if (Objects.isNull(outsourcingDTO)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数不能为空");
        }
        boolean res = contractInfoService.submitOutsourcing(outsourcingDTO);
        return CommonResult.success(true);
    }

    @ApiOperation("待处理评审-核定外委业务-保存")
    @PostMapping("/saveOutsourcing")
    public CommonResult<Boolean> saveOutsourcing(@RequestBody OutsourcingDTO outsourcingDTO) throws BusinessException {

        boolean res = contractInfoService.saveOrUpdateOutsourcing(outsourcingDTO);
        return CommonResult.success(true);
    }

    @ApiOperation("发起合同评审-保存")
    @PostMapping("/saveContractInfo")
    public CommonResult<Long> saveContractInfo(@RequestBody ContractInfoDTO contractInfoDto) throws BusinessException {
        // 保存时不校验
        Long id = contractInfoService.saveContractInfo(contractInfoDto);
        return CommonResult.success(id);
    }

    @ApiOperation("发起合同评审-提交")
    @PostMapping("/submitContractInfo")
    public CommonResult<Long> submitContractInfo(@RequestBody ContractInfoDTO contractInfoDto) throws BusinessException {
        // 提交时需要校验
        ValidationResult result = this.validator.validate(contractInfoDto);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        Long id = contractInfoService.submitReviews(contractInfoDto);
        return CommonResult.success(id);
    }

    @ApiOperation("销售公司-待处理评审-查询")
    @PostMapping("/getPendingReviews")
    public PageDataResult<SaleHomePendingReviewsVO> getPendingReviews(@RequestBody SaleHomePendingParam saleHomePendingParam) throws BusinessException {
        ValidationResult result = this.validator.validate(saleHomePendingParam);

        String userId = RequestContextHolder.getUserId();
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getPendingContractInfo(saleHomePendingParam);

        // 手动转换，避免类型不匹配问题
        List<SaleHomePendingReviewsVO> res = new ArrayList<>();
        for (ContractInfoDomain domain : pageDataInfo.getRows()) {
            SaleHomePendingReviewsVO vo = new SaleHomePendingReviewsVO();

            // 手动设置可能有类型不匹配的字段
            vo.setId(domain.getId() != null ? domain.getId().toString() : null);
            vo.setIsHead(domain.getIsHead() != null ? domain.getIsHead().byteValue() : null);

            // 使用BeanUtil复制其他字段
            BeanUtil.copyProperties(domain, vo, "id", "isHead");

            res.add(vo);
        }

        // 同时处理norm和specification字段
        List<ContractInfoDomain> domains = pageDataInfo.getRows();
        for (int i = 0; i < res.size() && i < domains.size(); i++) {
            try {
                SaleHomePendingReviewsVO vo = res.get(i);
                ContractInfoDomain domain = domains.get(i);

                // 处理norm字段
                String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                vo.setNorm(split);

                // 格式化规格显示名称
                String displaySpecification = formatSpecificationDisplay(domain);
                vo.setSpecification(displaySpecification);
            } catch (Exception e) {
                // 如果处理单个记录失败，记录日志但不影响其他记录
                log.warn("Failed to process specification display for record {}: {}", i, e.getMessage());
                // 确保norm字段仍然被设置
                if (i < res.size()) {
                    SaleHomePendingReviewsVO vo = res.get(i);
                    String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                    vo.setNorm(split);
                }
            }
        }
        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("销售公司-已发送评审-查询")
    @PostMapping("/getSentReviews")
    public PageDataResult<SaleHomeSentReviewsVO> getSentReviews(@RequestBody SaleHomeSentParam saleHomeSentParam) throws BusinessException {
        ValidationResult result = this.validator.validate(saleHomeSentParam);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getSentContractInfo(saleHomeSentParam);
        List<SaleHomeSentReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeSentReviewsVO.class);

        // 处理norm和specification字段
        List<ContractInfoDomain> domains = pageDataInfo.getRows();
        for (int i = 0; i < res.size() && i < domains.size(); i++) {
            try {
                SaleHomeSentReviewsVO vo = res.get(i);
                ContractInfoDomain domain = domains.get(i);

                // 处理norm字段
                String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                vo.setNorm(split);

                // 格式化规格显示名称
                String displaySpecification = formatSpecificationDisplay(domain);
                vo.setSpecification(displaySpecification);
            } catch (Exception e) {
                // 如果处理单个记录失败，记录日志但不影响其他记录
                log.warn("Failed to process specification display for record {}: {}", i, e.getMessage());
                // 确保norm字段仍然被设置
                if (i < res.size()) {
                    SaleHomeSentReviewsVO vo = res.get(i);
                    String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                    vo.setNorm(split);
                }
            }
        }
        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("销售公司-历史评审-查询")
    @PostMapping("/getHistoricalReviews")
    public PageDataResult<SaleHomeHistoricalReviewsVO> getHistoricalReviews(@RequestBody SaleHomeHistoricalParam saleHomeHistoricalParam) throws BusinessException {
        ValidationResult result = this.validator.validate(saleHomeHistoricalParam);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(saleHomeHistoricalParam);
        List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);

        // 处理norm、specification字段和评审意见
        List<ContractInfoDomain> domains = pageDataInfo.getRows();
        for (int i = 0; i < res.size() && i < domains.size(); i++) {
            try {
                SaleHomeHistoricalReviewsVO vo = res.get(i);
                ContractInfoDomain domain = domains.get(i);

                // 处理norm字段
                String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                vo.setNorm(split);

                // 格式化规格显示名称
                String displaySpecification = formatSpecificationDisplay(domain);
                vo.setSpecification(displaySpecification);

                // 查询科室主任的评审意见
                ReviewComment reviewComment = reviewCommentService.getReviewOpinionById(vo.getId());
                if (reviewComment != null) {
                    vo.setReceivingState(reviewComment.getReceivingState());
                    vo.setReceivingStateText(getReceivingStateText(reviewComment.getReceivingState()));
                    vo.setReceivingRemark(reviewComment.getReceivingRemark());
                    vo.setCostCalculation(reviewComment.getCostCalculation());
                    vo.setAssess(reviewComment.getAssess());
                    vo.setAssessText(getAssessText(reviewComment.getAssess()));
                    vo.setIsMakeReview(reviewComment.getIsMake());
                    vo.setIsMakeReviewText(getIsMakeText(reviewComment.getIsMake()));
                    vo.setOutsourcingStatusReview(reviewComment.getOutsourcingStatus());
                    vo.setIsCostByChange(reviewComment.getIsCostByChange());
                    vo.setIsCostByChangeText(getIsCostByChangeText(reviewComment.getIsCostByChange()));
                }
            } catch (Exception e) {
                // 如果处理单个记录失败，记录日志但不影响其他记录
                log.warn("Failed to process specification display for record {}: {}", i, e.getMessage());
                // 确保norm字段仍然被设置
                if (i < res.size()) {
                    SaleHomeHistoricalReviewsVO vo = res.get(i);
                    String[] split = vo.getValue1() != null ? vo.getValue1().split(",") : new String[0];
                    vo.setNorm(split);
                }
            }
        }
        return PageDataResult.success(res, pageDataInfo.getTotal());
    }

    @ApiOperation("销售公司-历史评审-下载")
    @PostMapping("/download")
    public ResponseEntity<byte[]> download(@RequestBody SaleHomeHistoricalParam saleHomeHistoricalParam) throws BusinessException, IOException {

        // 获取要下载的合同ID列表
        List<String> contractIds = getContractIds(saleHomeHistoricalParam);

        if (contractIds.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "没有找到要下载的数据");
        }

        if (contractIds.size() == 1) {
            // 单文件下载：返回Word文档
            return downloadSingleWord(contractIds.get(0));
        } else {
            // 批量下载：返回ZIP压缩包
            return downloadMultipleWordsAsZip(contractIds);
        }
    }

    /**
     * 获取要下载的合同ID列表
     */
    private List<String> getContractIds(SaleHomeHistoricalParam param) {
        List<String> contractIds = new ArrayList<>();

        // 优先使用contractInfoIds数组
        if (param.getContractInfoIds() != null && !param.getContractInfoIds().isEmpty()) {
            contractIds.addAll(param.getContractInfoIds());
        }
        // 兼容旧的contractInfoId字段
        else if (StringUtils.isNotBlank(param.getContractInfoId())) {
            contractIds.add(param.getContractInfoId());
        }

        return contractIds;
    }

    /**
     * 单文件Word下载
     */
    private ResponseEntity<byte[]> downloadSingleWord(String contractInfoId) throws BusinessException, IOException {
        // 创建查询参数，只查询指定的合同
        SaleHomeHistoricalParam param = new SaleHomeHistoricalParam();
        param.setValue4(contractInfoId);  // 使用value4字段，对应SQL中的#{value4}
        param.setPage(1);      // 设置页码（下载时不需要分页，设置默认值）
        param.setCurrent(1);   // 设置当前页（下载时不需要分页，设置默认值）

        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }

        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(param);
        List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);

        if (res.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "没有找到合同信息：" + contractInfoId);
        }

        // 只处理第一条记录（应该只有一条）
        SaleHomeHistoricalReviewsVO re = res.get(0);

        // 使用提取的公共方法生成数据
        Map<String, String> data = generateContractData(re, contractInfoId);

        // 调试：打印数据Map的内容
        System.out.println("=== 生成的数据Map ===");
        for (Map.Entry<String, String> entry : data.entrySet()) {
            System.out.println(entry.getKey() + " = " + entry.getValue());
        }

        for (Map.Entry<String, String> stringStringEntry : data.entrySet()) {
            if (stringStringEntry.getValue() == null) {
                stringStringEntry.setValue(" ");
            }
        }

            // 使用配置文件中的模板路径
            String templatePath = templateBasePath + reviewTemplateName;

            // 调试：打印文件路径
            System.out.println("=== 文件路径调试 ===");
            System.out.println("模板文件路径: " + templatePath);
            System.out.println("模板文件是否存在: " + Files.exists(Paths.get(templatePath)));

            // 创建临时文件用于生成Word
            String tempWordPath = System.getProperty("java.io.tmpdir") + "contract_review_" + System.currentTimeMillis() + ".docx";
            System.out.println("临时文件路径: " + tempWordPath);

            // 替换模板内容
            String processedWordPath = WordTableTextReplacer.replaceTextInTable(templatePath, tempWordPath, data);
            System.out.println("处理后文件路径: " + processedWordPath);
            System.out.println("处理后文件是否存在: " + Files.exists(Paths.get(processedWordPath)));

            // 读取处理后的Word文件
            byte[] wordBytes = Files.readAllBytes(Paths.get(processedWordPath));

            // 清理临时文件
            Files.deleteIfExists(Paths.get(processedWordPath));

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            String originalFilename = re.getCode() + "_合同评审记录.docx";  // 原始文件名（包含中文）
            String encodedFilename = URLEncoder.encode(originalFilename, "UTF-8");  // URL编码后的文件名

            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            // 使用RFC 5987标准设置文件名，支持中文
            headers.add("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);
            headers.add("download-filename", encodedFilename);  // 添加自定义文件名头（编码后），方便前端获取
            headers.add("Access-Control-Expose-Headers", "download-filename, content-disposition");  // 允许前端访问自定义响应头

            // 调试：打印响应头信息
            System.out.println("=== 响应头调试 ===");
            System.out.println("原始文件名: " + originalFilename);
            System.out.println("编码后文件名: " + encodedFilename);
            System.out.println("Content-Type: " + headers.getContentType());
            System.out.println("Content-Disposition: " + headers.getContentDisposition());
            System.out.println("download-filename: " + headers.get("download-filename"));
            System.out.println("注意：download-filename使用编码后的文件名，前端需要解码");
            System.out.println("Access-Control-Expose-Headers: " + headers.get("Access-Control-Expose-Headers"));

            // 返回Word文件流
            return ResponseEntity.ok()
                .headers(headers)
                .body(wordBytes);
    }

    /**
     * 批量Word下载（ZIP压缩包）
     */
    private ResponseEntity<byte[]> downloadMultipleWordsAsZip(List<String> contractIds) throws BusinessException, IOException {
        if (contractIds.size() > 10) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "单次最多只能下载10个文件");
        }

        ByteArrayOutputStream zipStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(zipStream);

        try {
            for (String contractId : contractIds) {
                try {
                    // 生成单个Word的字节数组
                    byte[] wordBytes = generateSingleWordBytes(contractId);

                    // 获取合同编号用于文件名
                    SaleHomeHistoricalParam param = new SaleHomeHistoricalParam();
                    param.setValue4(contractId);
                    param.setPage(1);
                    param.setCurrent(1);
                    PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(param);
                    List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);
                    String contractCode = res.isEmpty() ? contractId : res.get(0).getCode();

                    // 添加到ZIP
                    ZipEntry entry = new ZipEntry(contractCode + "_合同评审记录.docx");
                    zip.putNextEntry(entry);
                    zip.write(wordBytes);
                    zip.closeEntry();
                } catch (Exception e) {
                    log.warn("生成合同Word失败，合同ID: {}, 错误: {}", contractId, e.getMessage());
                    // 继续处理其他文件，不中断整个流程
                }
            }
        } finally {
            zip.close();
        }

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        // 使用与单文件下载相同的文件名处理方式
        String originalFilename = "contract_reviews_" + System.currentTimeMillis() + ".zip";
        try {
            String encodedFilename = URLEncoder.encode(originalFilename, "UTF-8");
            headers.add("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);
            headers.add("download-filename", encodedFilename);
            headers.add("Access-Control-Expose-Headers", "download-filename, content-disposition");
        } catch (UnsupportedEncodingException e) {
            // 如果编码失败，使用原始文件名
            headers.setContentDisposition(ContentDisposition.attachment()
                .filename(originalFilename)
                .build());
        }

        return ResponseEntity.ok()
            .headers(headers)
            .body(zipStream.toByteArray());
    }

    /**
     * 生成单个Word的字节数组（用于批量下载）
     */
    private byte[] generateSingleWordBytes(String contractInfoId) throws Exception {
        // 创建查询参数
        SaleHomeHistoricalParam param = new SaleHomeHistoricalParam();
        param.setValue4(contractInfoId);  // 使用value4字段，对应SQL中的#{value4}
        param.setPage(1);      // 设置页码（下载时不需要分页，设置默认值）
        param.setCurrent(1);   // 设置当前页（下载时不需要分页，设置默认值）

        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getHistoricalContractInfo(param);
        List<SaleHomeHistoricalReviewsVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), SaleHomeHistoricalReviewsVO.class);

        if (res.isEmpty()) {
            throw new RuntimeException("没有找到合同信息：" + contractInfoId);
        }

        // 处理数据（复用单文件下载的逻辑）
        SaleHomeHistoricalReviewsVO re = res.get(0);
        Map<String, String> data = generateContractData(re, contractInfoId);

        // 生成Word文档
        String templatePath = templateBasePath + reviewTemplateName;
        String tempWordPath = System.getProperty("java.io.tmpdir") + "contract_review_" + contractInfoId + "_" + System.currentTimeMillis() + ".docx";

        String processedWordPath = WordTableTextReplacer.replaceTextInTable(templatePath, tempWordPath, data);

        // 读取处理后的Word文件
        byte[] wordBytes = Files.readAllBytes(Paths.get(processedWordPath));

        // 清理临时文件
        Files.deleteIfExists(Paths.get(processedWordPath));

        return wordBytes;
    }

    /**
     * 生成合同数据Map（提取公共逻辑）
     */
    private Map<String, String> generateContractData(SaleHomeHistoricalReviewsVO re, String contractInfoId) {
        Map<String, String> data = new HashMap<>();

        // 将String类型的contractInfoId转换为Long类型，用于数据库查询
        Long contractId = Long.parseLong(contractInfoId);

        String[] split = re.getValue1() != null ? re.getValue1().split(",") : new String[0];
        re.setNorm(split);
        LambdaQueryWrapper<SpecificationBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SpecificationBase::getSpecification, re.getSpecification());
        SpecificationBase byId = specificationBaseService.getOne(lambdaQueryWrapper);
        String specification = byId.getSpecification();

        String getValue1 = re.getValue1() != null ? re.getValue1() + "mm" : " ";
        String getValue2 = re.getValue2() != null ? "x" + re.getValue2() + "mm" : " ";
        String getValue3 = re.getValue3() != null ? "x" + re.getValue3() + "mm" : " ";
        data.put("{steelGrade}", re.getSteelGradeName());
        data.put("{phone}", re.getCustomerPhone());
        data.put("{name}", re.getCustomerName());
        data.put("{steelType1}", "汽车钢".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelType2}", "航空、航天及甲类".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelType3}", "核电".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelType4}", "API".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelType5}", "其它".equals(re.getSteelTypeName()) ? "√" : " ");
        data.put("{steelSpecification}", specification);
        data.put("{button}", getValue1 + getValue2 + getValue3);
        data.put("{specialRequirements}", re.getSpecialRequirements() != null ? re.getSpecialRequirements() : "");
        data.put("{processingPurpose}", re.getProcessingPurpose() != null ? re.getProcessingPurpose() : "");
        data.put("{standard}", re.getStandardName() != null ? re.getStandardName() : "");
        data.put("{technicalStandard}", re.getTechnicalStandardName() != null ? re.getTechnicalStandardName() : "");
        data.put("{authorName}", re.getAuthorName() != null ? re.getAuthorName() : "");
        data.put("{salesmanName}", re.getSalesmanName() != null ? re.getSalesmanName() : "");

        // 继续处理其他数据...
        ContractInfo byId1 = contractInfoService.getById(contractId);
        ReviewComment reviewComment = reviewCommentService.getReviewOpinionById(contractId.toString());

        List<ReviewInfo> reviewInfos = reviewInfoService.getReviewInfoById(String.valueOf(reviewComment.getId()));

        // 处理评审意见（最多5个）
        for (int i = 0; i < 5; i++) {
            int index = i + 1;
            if (i < reviewInfos.size()) {
                ReviewInfo reviewInfo = reviewInfos.get(i);
                String comment = reviewInfo.getCommentModified() != null ? reviewInfo.getCommentModified() : reviewInfo.getReviewComment();
                String assessor = reviewInfo.getAssessor();
                String department = reviewInfo.getDepartment();
                data.put("B" + index, department != null ? department : " ");
                data.put("C" + index, assessor != null ? assessor : " ");
                data.put("A" + index, comment != null ? comment : " ");
            } else {
                data.put("B" + index, " ");
                data.put("C" + index, " ");
                data.put("A" + index, " ");
            }
        }

        // 处理评审结果数据
        ReviewCommentInfoDtoDto reviewCommentInfoDto = new ReviewCommentInfoDtoDto();
        BeanUtil.copyProperties(reviewComment, reviewCommentInfoDto);
        Integer isMake = reviewCommentInfoDto.getIsMake();
        Integer assess = reviewCommentInfoDto.getAssess();
        Long isCostByChange = reviewCommentInfoDto.getIsCostByChange();
        String receivingRemark = reviewCommentInfoDto.getReceivingRemark();

        data.put("{receivingRemark}", receivingRemark != null ? receivingRemark : "");
        data.put("f1", isCostByChange == 1 ? "√" : " ");
        data.put("f2", isCostByChange == 0 ? "√" : " ");
        data.put("r1", isMake == 1 ? "√" : " ");
        data.put("r2", isMake == 0 ? "√" : " ");
        data.put("{assess1}", assess == 0 ? "√" : " ");
        data.put("{assess2}", assess == 1 ? "√" : " ");
        data.put("{assess3}", assess == 2 ? "√" : " ");

        // 处理最终意见数据
        if (byId1.getOverallOpinionId() != null) {
            String remark = overallOpinionService.getById(byId1.getOverallOpinionId()).getRemark();
            data.put("{remark}", remark != null ? remark : "");
        }

        if (byId1.getFinalOpinionId() != null) {
            LambdaQueryWrapper<FinalOpinion> eq = new LambdaQueryWrapper<>();
            eq.eq(FinalOpinion::getId, byId1.getFinalOpinionId());
            FinalOpinion one = finalOpinionService.getOne(eq);
            if (one != null) {
                Integer isConclude = one.getIsConclude();
                Integer isOrderGood = one.getIsOrderGood();
                Integer productType = one.getProductType();
                String annotatedContent = one.getAnnotatedContent();

                data.put("{isOrderGood1}", isOrderGood == 0 ? "√" : " ");
                data.put("{isOrderGood2}", isOrderGood == 1 ? "√" : " ");
                data.put("{productType1}", productType == 0 ? "√" : " ");
                data.put("{productType2}", productType == 1 ? "√" : " ");
                data.put("uutt", isConclude == 0 ? "√" : " ");
                data.put("sssss", isConclude == 1 ? "√" : " ");
                data.put("{annotatedContent}", annotatedContent != null ? annotatedContent : "");
            }
        }

        // 添加时间和ID
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        data.put("{createTime}", re.getCreateTime().format(formatter1));
        data.put("{id}", contractInfoId);

        // 处理空值
        for (Map.Entry<String, String> entry : data.entrySet()) {
            if (entry.getValue() == null) {
                entry.setValue(" ");
            }
        }

        return data;
    }

    @ApiOperation("发起合同评审-查询技术条件")
    @GetMapping("/getTechStandardList")
    public CommonResult<List<TechnicalStandardBase>> getTechStandardList(@ApiParam(value = "{\"technicalStandardName\":\"\"}") @RequestParam(required = false) Map<String, String> param) {
        String keyword = param.get("technicalStandardName");

        String userId = RequestContextHolder.getUserId();
        LambdaQueryWrapper<TechnicalStandardBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(TechnicalStandardBase::getTechnicalStandardName, keyword);
        }
        return CommonResult.success(technicalStandardBaseService.list(queryWrapper));
    }


    @ApiOperation("发起合同评审-查询钢种")
    @PostMapping("/getSteelGradeListByName")
    public CommonResult<List<SteelGradeBase>> getSteelGradeListByName(@ApiParam(value = "{\"steelGradeName\":\"\"}") @RequestBody(required = false) Map<String, String> param) throws BusinessException {
        String steelGradeName = "";
        String userId = RequestContextHolder.getUserId();
        if (param != null && !param.isEmpty()) {
            steelGradeName = param.get("steelGradeName");
        }
        List<SteelGradeBase> list = steelGradeBaseService.getSteelGradeListByName(steelGradeName);
        return CommonResult.success(list);
    }

    @ApiOperation("发起合同评审-查询钢类")
    @PostMapping("/getSteelTypeListByName")
    public CommonResult<List<SteelTypeBase>> getSteelTypeListByName(@ApiParam(value = "{\"steelTypeName\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String steelTypeName = "";
        if (param != null && !param.isEmpty()) {
            steelTypeName = param.get("steelTypeName");
        }
        List<SteelTypeBase> list = steelTypeBaseService.getSteelTypeListByName(steelTypeName.trim());
        return CommonResult.success(list);
    }

    @ApiOperation("发起合同评审-待处理事项")
    @PostMapping("/getItemBase")
    public CommonResult<List<ItemBase>> getItemBase(@ApiParam(value = "{\"itemName\":\"\"}") @RequestBody(required = false) Map<String, String> map) {

        String itemName = "";
        if (map != null && !map.isEmpty()) {
            itemName = map.get("itemName");
        }
        List<ItemBase> list = itemBaseService.getItemBase(itemName.trim());
        return CommonResult.success(list);
    }


    @ApiOperation("发起合同评审-查询顾客")
    @PostMapping("/getCustomerListByName")
    public CommonResult<List<CustomerInfoVO>> getCustomerListByName(@ApiParam(value = "{\"customerName\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        if (map == null || map.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数为空");
        }
        String customerName = map.get("customerName");
        List<CustomerInfo> customerInfoList = customerInfoService.getCustomerInfoListByName(customerName.trim());
        List<CustomerInfoVO> customerInfoVOList = BeanUtil.copyToList(customerInfoList, CustomerInfoVO.class);
        return CommonResult.success(customerInfoVOList);
    }

    @ApiOperation("发起合同评审-查询标准")
    @GetMapping("/getStandardList")
    public CommonResult<List<StandardBase>> getStandardList(@ApiParam(value = "{\"standard_name\":\"\"}") @RequestParam(required = false) Map<String, String> param) {
        String keyword = param.get("standard_name");
        LambdaQueryWrapper<StandardBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(StandardBase::getStandardName, keyword);
        }
        return CommonResult.success(standardBaseService.list(queryWrapper));
    }

    @ApiOperation("发起合同评审-查询交货状态")
    @PostMapping("/getDeliveryStatusList")
    public CommonResult<List<DeliveryStatusBase>> getDeliveryStatusList(@ApiParam(value = "{\"delivery_status\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String keyword = param.get("delivery_status");
        LambdaQueryWrapper<DeliveryStatusBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(DeliveryStatusBase::getDeliveryStatus, keyword);
        }
        return CommonResult.success(deliveryStatusBaseService.list(queryWrapper));
    }

    @ApiOperation("发起合同评审-查询加工用途")
    @PostMapping("/getProcessingPurposeList")
    public CommonResult<List<ProcessingPurposeBase>> getProcPurposeList(@ApiParam(value = "{\"processing_purpose\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String keyword = param.get("processing_purpose");
        LambdaQueryWrapper<ProcessingPurposeBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(ProcessingPurposeBase::getProcessingPurpose, keyword);
        }
        return CommonResult.success(processingPurposeBaseService.list(queryWrapper));
    }

    @ApiOperation("发起合同评审-查询规格")
    @PostMapping("/getSpecificationBaseList")
    public CommonResult<List<SpecificationBaseVo>> getSpecificationBaseList(
            @ApiParam(value = "{\"specification\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String specification = param == null ? null : param.get("specification");
        List<SpecificationBase> list = specificationBaseService.getSpecificationBaseListByKeyword(specification);
        List<SpecificationBaseVo> specificationBaseVos = new ArrayList<>();
        for (SpecificationBase specificationBaseVo : list) {
            String[] strings = specificationBaseVo.getButton() != null ? specificationBaseVo.getButton().split(",") : null;
            SpecificationBaseVo specificationBaseVo1 = new SpecificationBaseVo();
            specificationBaseVo1.setSpecification(specificationBaseVo.getSpecification());
            specificationBaseVo1.setId(specificationBaseVo.getId());
            specificationBaseVo1.setButton(strings);
            specificationBaseVo1.setSpecificationNote(specificationBaseVo.getSpecificationNote());
            // 不设置displayName，保持原有功能
            specificationBaseVos.add(specificationBaseVo1);
        }
        return CommonResult.success(specificationBaseVos);
    }

    @ApiOperation("待处理评审-核定外委业务-查询外委厂商")
    @PostMapping("/getOutsourcingList")
    public CommonResult<List<Outsourcing>> getOutsourcingList(@ApiParam(value = "{\"outsourcingName\":\"\",\"outsourcingPhone\":\"\"}") @RequestBody Map<String, String> map) throws BusinessException {
        if (map == null || map.isEmpty()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数不能为空");
        }
        Outsourcing outsourcing = BeanUtil.mapToBean(map, Outsourcing.class, true);
        if (Objects.isNull(outsourcing)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "参数不能为空");
        }
        List<Outsourcing> list = outsourcingService.list(new LambdaQueryWrapper<Outsourcing>()
                .like(!(Objects.isNull(outsourcing.getOutsourcingName()) || outsourcing.getOutsourcingName().isEmpty()),
                        Outsourcing::getOutsourcingName, outsourcing.getOutsourcingName())
                .like(!(Objects.isNull(outsourcing.getOutsourcingPhone()) || outsourcing.getOutsourcingPhone().isEmpty()),
                        Outsourcing::getOutsourcingPhone, outsourcing.getOutsourcingPhone()));
        return CommonResult.success(list);
    }

    @ApiOperation("查询当前状态")
    @PostMapping("/getStatus")
    public CommonResult<List<StatusBase>> getStatus(@ApiParam(value = "{\"statusName\":\"\"}") @RequestBody(required = false) Map<String, String> param) {
        String keyword = param.get("statusName");
        LambdaQueryWrapper<StatusBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(StatusBase::getStatusName, keyword);
        }
        return CommonResult.success(statusBaseMapper.selectList(queryWrapper));
    }

    @ApiOperation("查询审核类型下拉")
    @GetMapping("/getReviewType")
    public CommonResult<List<ReviewTypeBase>> getReviewType() {
        return CommonResult.success(reviewTypeService.list());
    }

    /**
     * 格式化规格显示名称
     * @param domain 合同信息域对象
     * @return 拼接后的显示名称
     */
    private String formatSpecificationDisplay(ContractInfoDomain domain) {
        // 安全检查
        if (domain == null || domain.getSpecification() == null) {
            return "";
        }

        // 从已拼接的specification中提取原始规格名称和备注
        String originalSpec = domain.getSpecification();
        String specName = originalSpec;
        String specNote = "";

        // 尝试分离规格名称和备注（格式：规格名称 备注）
        int spaceIndex = originalSpec.indexOf(' ');
        if (spaceIndex > 0) {
            specName = originalSpec.substring(0, spaceIndex);
            specNote = originalSpec.substring(spaceIndex + 1);
        }

        StringBuilder result = new StringBuilder(specName);

        // 获取具体尺寸信息和维度信息
        if (domain.getValue1() != null && !domain.getValue1().isEmpty() && domain.getSteelSpecificationId() != null) {
            try {
                // 根据steelSpecificationId查询SpecificationInfo，然后获取SpecificationBase的button信息
                SpecificationInfo specInfo = specificationInfoService.getById(domain.getSteelSpecificationId());
                if (specInfo != null && specInfo.getSpecificationId() != null) {
                    SpecificationBase specBase = specificationBaseService.getById(specInfo.getSpecificationId());
                    if (specBase != null && specBase.getButton() != null && !specBase.getButton().isEmpty()) {
                        // 使用完整的拼接逻辑
                        String[] dimensions = specBase.getButton().split(",");
                        String[] values = domain.getValue1().split(",");

                        result.append(":");
                        for (int i = 0; i < Math.min(dimensions.length, values.length); i++) {
                            if (i > 0) {
                                result.append("*");  // 添加维度分隔符
                            }
                            result.append(dimensions[i]).append(values[i]).append("mm");
                        }
                    } else {
                        // 如果没有维度信息，使用简单拼接
                        result.append(" ").append(domain.getValue1());
                    }
                } else {
                    // 如果查询不到规格信息，使用简单拼接
                    result.append(" ").append(domain.getValue1());
                }
            } catch (Exception e) {
                // 如果查询失败，使用简单拼接
                log.warn("Failed to format specification display for domain: {}", domain.getId(), e);
                result.append(" ").append(domain.getValue1());
            }
        }

        // 添加规格备注
        if (specNote != null && !specNote.isEmpty()) {
            result.append("（").append(specNote).append("）");
        }

        return result.toString();
    }

    @ApiOperation("获取发起人下拉框选项")
    @GetMapping("/getSubmitUserOptions")
    public CommonResult<List<String>> getSubmitUserOptions(@RequestParam(required = false) String name) {
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<Employee>()
            .select(Employee::getNickname)
            .orderBy(true, true, Employee::getNickname);

        // 如果传入了名字，进行模糊查询
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(Employee::getNickname, name);
        }

        List<Employee> employees = userService.list(queryWrapper);

        List<String> names = employees.stream()
            .map(Employee::getNickname)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        return CommonResult.success(names);
    }

    /**
     * 获取接单状态文字描述
     * @param receivingState 接单状态
     * @return 状态描述
     */
    private String getReceivingStateText(Integer receivingState) {
        if (receivingState == null) return null;
        switch (receivingState) {
            case 0: return "拒单";
            case 1: return "接单";
            case 2: return "条件接单";
            default: return "未知状态";
        }
    }

    /**
     * 获取风险等级评估文字描述
     * @param assess 风险等级评估
     * @return 评估描述
     */
    private String getAssessText(Integer assess) {
        if (assess == null) return null;
        switch (assess) {
            case 0: return "低风险";
            case 1: return "中风险";
            case 2: return "高风险";
            default: return "未知风险";
        }
    }

    /**
     * 获取首试制文字描述
     * @param isMake 首试制
     * @return 首试制描述
     */
    private String getIsMakeText(Integer isMake) {
        if (isMake == null) return null;
        switch (isMake) {
            case 0: return "否";
            case 1: return "是";
            default: return "未知";
        }
    }

    /**
     * 获取是否引起成本变化文字描述
     * @param isCostByChange 是否引起成本变化
     * @return 成本变化描述
     */
    private String getIsCostByChangeText(Long isCostByChange) {
        if (isCostByChange == null) return null;
        switch (isCostByChange.intValue()) {
            case 0: return "否";
            case 1: return "是";
            default: return "未知";
        }
    }

}
