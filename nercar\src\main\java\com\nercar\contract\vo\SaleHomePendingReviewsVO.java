package com.nercar.contract.vo;

import com.nercar.contract.enums.SteelNumerUnitEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @description: 首页数据
* @author: zmc
* @date: 2024/9/21
*/
@Data
public class SaleHomePendingReviewsVO {
    private String id;
    private String code;
    private String customerName;
    private String customerPhone;
    private String steelTypeName;
    private String steelGradeName;
    private String deliveryStatus;
    private String specification;
    private String standardName;
    private Integer steelNumber;
    private SteelNumerUnitEnum steelNumberUnit;
    private String processingPurpose;
    private String itemName;

    private Byte isHead;
    private String value1;
    private String value2;
    private String value3;
    private String[] norm;
    private LocalDateTime submitTime;
    private String creater_id;
    private String type;
}
