package com.nercar.contract.Filter;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/08 15:05
 */
public class RequestContextHolder {
    private static final ThreadLocal<String> userIdHolder = new ThreadLocal<>();

    public static void setUserId(String userId) {
        userIdHolder.set(userId);
    }

    public static String getUserId() {
        return userIdHolder.get();
    }

    public static void clear() {
        userIdHolder.remove(); // 清除当前线程的数据，防止内存泄漏
    }
}