package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.Filter.RequestContextHolder;
import com.nercar.contract.enums.DeptConstant;
import com.nercar.contract.entity.ProcessFlow;
import com.nercar.contract.enums.StepEnum;
import com.nercar.contract.mapper.ProcessFlowMapper;
import com.nercar.contract.service.ProcessFlowService;
import com.nercar.contract.utils.IdGenerator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/08 12:03
 */
@Service
public class ProcessFlowServiceImpl extends ServiceImpl<ProcessFlowMapper, ProcessFlow> implements ProcessFlowService {




    @Override
    @Transactional
    public int insert(Long contractInfoId, StepEnum currentStep) {
        ProcessFlow processFlow = new ProcessFlow();
        processFlow.setId(IdGenerator.generateNumericUUID());
        processFlow.setContractInfoId(contractInfoId);
        processFlow.setCurrentStep(currentStep);
        extracted(currentStep.getCode(), processFlow);
        String userId = RequestContextHolder.getUserId();
        processFlow.setCreateUser(userId);
        processFlow.setCreateTime(LocalDateTime.now());
        return baseMapper.insert(processFlow);
    }

    @Override
    @Transactional
    public int insert(Long contractInfoId, StepEnum currentStep, String customDept) {
        ProcessFlow processFlow = new ProcessFlow();
        processFlow.setId(IdGenerator.generateNumericUUID());
        processFlow.setContractInfoId(contractInfoId);
        processFlow.setCurrentStep(currentStep);
        // 使用自定义部门而不是默认的部门设置逻辑
        processFlow.setCurrentDept(customDept);
        String userId = RequestContextHolder.getUserId();
        processFlow.setCreateUser(userId);
        processFlow.setCreateTime(LocalDateTime.now());
        return baseMapper.insert(processFlow);
    }

    @Override
    @Transactional
    public boolean deleteAll(Long contractInfoId) {
        LambdaQueryWrapper<ProcessFlow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessFlow::getContractInfoId, contractInfoId);
        boolean remove = this.remove(queryWrapper);
        return  remove;

    }

    @Override
    public LocalDateTime getReviewTime(Long contractInfoId, StepEnum currentStep) {
        LambdaQueryWrapper<ProcessFlow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessFlow::getContractInfoId, contractInfoId);
        queryWrapper.orderByDesc(ProcessFlow::getCreateTime);
        // queryWrapper.eq(ProcessFlow::getCurrentStep,currentStep.getCode());
        queryWrapper.eq(ProcessFlow::getCurrentStep,currentStep);

        List<ProcessFlow> list = this.list(queryWrapper);
        if  (list.isEmpty()) {
            return null;
        }
        return   list.get(0).getCreateTime();
    }

    private static void extracted(String currentStep, ProcessFlow processFlow) {
        switch (currentStep){
            case "1":
                processFlow.setCurrentDept(DeptConstant.XIAOSHOU);
                break;

            case "2":
                processFlow.setCurrentDept(DeptConstant.BIAOZHUNKE);
                break;

            case "3":
                processFlow.setCurrentDept(DeptConstant.XIAOSHOU);
                break;

            case "4":
                processFlow.setCurrentDept(DeptConstant.PINGSHENRENYUAN);
                break;

            case "5":
                processFlow.setCurrentDept(DeptConstant.XIAOSHOU);
                break;

            case "6":
                processFlow.setCurrentDept(DeptConstant.BIAOZHUNKE);
                break;

            case "7":
                processFlow.setCurrentDept(DeptConstant.ZHUREN);
                break;

            case "8":
                processFlow.setCurrentDept(DeptConstant.XIAOSHOU);
                break;

            case "9":
                processFlow.setCurrentDept(DeptConstant.GUIDANG);
                break;
            case "10":
                processFlow.setCurrentDept(DeptConstant.OA);
                break;
            case "11":
                processFlow.setCurrentDept(DeptConstant.BIAOZHUNKE);
                break;
            case "12":
                processFlow.setCurrentDept(DeptConstant.PINGSHENRENYUAN);
                break;
        }
    }
}
